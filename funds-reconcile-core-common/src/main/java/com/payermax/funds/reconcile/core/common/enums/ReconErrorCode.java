package com.payermax.funds.reconcile.core.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ReconErrorCode {

    BUSINESS_ERROR("BUSINESS_ERROR", "business error"),

    CONFIG_ERROR("CONFIG_ERROR", "config error"),

    TIME_OUT_ERROR("TIME_OUT_ERROR", "timeOut error"),

    DUPLICATE_ORDER("DUPLICATE_ORDER", "duplicate info"),

    BILL_CODE_QUERY_ERROR("BILL_CODE_QUERY_ERROR", "bill code query error"),

    RECON_RULE_NOT_EXIST("BILL_CODE_QUERY_ERROR", "recon rule not exist"),

    RECON_RULE_IS_NOT_VALID("RECON_RULE_IS_NOT_VALID", "recon rule is not valid"),

    RECON_STATUS_NOT_EXIST("RECON_STATUS_NOT_EXIST", "不支持的批次状态"),

    RECON_IS_RUNNING("RECON_IS_RUNNING", "对账正在执行中"),

    CHARGE_ON_TYPE_NOT_MATCH("CHARGE_ON_TYPE_NOT_MATCH", "匹配挂账类型失败"),

    CURRENCY_NOT_EQUALS("CURRENCY_NOT_EQUALS", "currency not equals"),


    INVALID_RECON_BATCH_ERROR("INVALID_RECON_BATCH_ERROR", "批次终止异常，请联系研发"),

    INVALID_TASK_ERROR("INVALID_TASK_ERROR", "invalid task error"),

    REVIEW_RECON_BATCH_ERROR("REVIEW_RECON_BATCH_ERROR", "批次审核异常，请联系研发"),

    ACTUAL_FUNDS_ALLOCATE_DUPLICATE_ITEM("ACTUAL_FUNDS_ALLOCATE_DUPLICATE_ITEM", "实体资金拆分-出现重复数据"),

    ACTUAL_FUNDS_ALLOCATE_VALIDATE_ERROR("ACTUAL_FUNDS_ALLOCATE_VALIDATE_ERROR", "实体资金拆分-校验异常"),

    ACTUAL_FUNDS_ALLOCATE_CONTEXT_VALIDATE_ERROR("ACTUAL_FUNDS_ALLOCATE_CONTEXT_VALIDATE_ERROR", "实体资金拆分-上下文校验异常"),

    SYSTEM_ERROR("SYSTEM_ERROR", "system inner error!"),

    ALL_NO_IS_EMPTY("ALL_NO_IS_EMPTY", "all no is empty"),

    ORIGINAL_BATCH_NO_IS_EXIST("ORIGINAL_BATCH_NO_IS_EXIST", "original batch no is exist"),

    REPEAT_EXCEEDS_LIMIT("REPEAT_EXCEEDS_LIMIT", "the number of repetitions exceeds the upper limit"),

    BATCH_DUPLICATE("BATCH_DUPLICATE", "batch contains duplicate data"),

    CAN_NOT_CREATE_BATCH("CAN_NOT_CREATE_BATCH", "can not create batch"),

    COMMON_TASK_SAVE_ERROR("COMMON_TASK_SAVE_ERROR", "common task save error"),
    ;

    private final String code;

    private final String msg;
}
