package com.payermax.funds.reconcile.core.infrastructure.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.payermax.common.lang.model.dto.Page;
import com.payermax.common.lang.util.StringUtil;
import com.payermax.funds.reconcile.core.common.utils.BizUtil;
import com.payermax.funds.reconcile.core.domain.domain.BatchResultAmount;
import com.payermax.funds.reconcile.core.domain.domain.BatchResultCount;
import com.payermax.funds.reconcile.core.domain.domain.ClearingReconcileTotalResult;
import com.payermax.funds.reconcile.core.domain.domain.ReconcileBatchResult;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingReconcileResult;
import com.payermax.funds.reconcile.core.infrastructure.repository.base.BaseRepositoryImpl;
import com.payermax.funds.reconcile.core.infrastructure.repository.condition.ClearingReconcileResultCondition;
import com.payermax.funds.reconcile.core.infrastructure.repository.converter.ClearingReconcileResultPOConverter;
import com.payermax.funds.reconcile.core.infrastructure.repository.converter.ReconcileBatchResultPOConverter;
import com.payermax.funds.reconcile.core.infrastructure.repository.entity.BatchResultAmountPO;
import com.payermax.funds.reconcile.core.infrastructure.repository.entity.BatchResultCountPO;
import com.payermax.funds.reconcile.core.infrastructure.repository.entity.ClearingReconcileResultPO;
import com.payermax.funds.reconcile.core.infrastructure.repository.entity.ReconcileBatchResultPO;
import com.payermax.funds.reconcile.core.infrastructure.repository.mapper.ClearingReconcileResultMapper;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.ClearingReconcileResultRepository;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 清算对账结果 Dao实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Repository("clearingReconcileResultRepository")
public class ClearingReconcileResultRepositoryImpl extends BaseRepositoryImpl<ClearingReconcileResultMapper, ClearingReconcileResultPO> implements ClearingReconcileResultRepository {


    @Resource
    private ClearingReconcileResultMapper clearingReconcileResultMapper;

    @Override
    public void saveBatch(List<ClearingReconcileResult> dataList) {
        List<ClearingReconcileResultPO> poList = ClearingReconcileResultPOConverter.INSTANCE.toClearingReconcileResultPOList(dataList);
        this.saveBatch(poList);
    }

    @Override
    public void updateBatchById(List<ClearingReconcileResult> dataList) {
        List<ClearingReconcileResultPO> poList = ClearingReconcileResultPOConverter.INSTANCE.toClearingReconcileResultPOList(dataList);
        this.updateBatchById(poList);
    }

    @Override
    public List<BatchResultCount> statistics(String batchNo) {

        List<BatchResultCountPO> list = clearingReconcileResultMapper.statistics(batchNo);
        List<BatchResultCount> resultCounts = ClearingReconcileResultPOConverter.INSTANCE.toBatchResultCount(list);

        return resultCounts;
    }

    @Override
    public ReconcileBatchResult timeStatistics(String batchNo) {

        ReconcileBatchResultPO resultPO = clearingReconcileResultMapper.timeStatistics(batchNo);

        return ReconcileBatchResultPOConverter.INSTANCE.toReconcileBatchResult(resultPO);
    }

    @Override
    public List<BatchResultAmount> amountStatistics(String batchNo) {

        List<BatchResultAmountPO> poList = clearingReconcileResultMapper.amountStatistics(batchNo);

        return ClearingReconcileResultPOConverter.INSTANCE.toBatchResultAmountList(poList);
    }

    @Override
    public void invalidReconResult(String batchNo) {
        clearingReconcileResultMapper.invalidReconResult(batchNo);
    }

    @Override
    public int queryCount(String batchNo, Boolean forSettleOrderNo, List<String> reconcileResult) {
        return clearingReconcileResultMapper.queryCount(batchNo, forSettleOrderNo, reconcileResult);
    }

    @Override
    public List<ClearingReconcileResult> queryByPage(String batchNo, Integer page, Integer pageSize, Boolean forSettleOrderNo, List<String> reconcileResult) {
        List<ClearingReconcileResultPO> poList = clearingReconcileResultMapper.queryByPage(batchNo, page * pageSize, pageSize, forSettleOrderNo, reconcileResult);
        return ClearingReconcileResultPOConverter.INSTANCE.toClearingReconcileResultList(poList);
    }

    @Override
    public List<ClearingReconcileResult> queryByOldKeyList(String batchNo, List<String> oldKeyList) {
        if (CollectionUtils.isEmpty(oldKeyList)) {
            return Lists.newArrayList();
        }
        List<ClearingReconcileResultPO> poList = clearingReconcileResultMapper.queryByOldKeyList(batchNo, oldKeyList);
        return ClearingReconcileResultPOConverter.INSTANCE.toClearingReconcileResultList(poList);
    }

    @Override
    public void updateSettleOrderNo(String settleOrderNo, String batchNo, List<String> resultIdList) {
        clearingReconcileResultMapper.updateSettleOrderNo(settleOrderNo, batchNo, resultIdList);
    }

    @Override
    public int queryCountBySettleOrderNo(String settleOrderNo, String reconcileResult) {
        ClearingReconcileResultCondition condition = new ClearingReconcileResultCondition();
        condition.setSettleOrderNo(settleOrderNo);
        if (StringUtil.isNotEmpty(reconcileResult)) {
            condition.setReconcileResult(reconcileResult);
        }
        return this.count(condition);
    }

    @Override
    public List<ClearingReconcileResult> queryBySettleOrderNoPage(String settleOrderNo, String reconcileResult, Integer page, Integer pageSize) {
        List<ClearingReconcileResultPO> poList = clearingReconcileResultMapper.queryBySettleOrderNoPage(settleOrderNo, reconcileResult, page * pageSize, pageSize);
        return ClearingReconcileResultPOConverter.INSTANCE.toClearingReconcileResultList(poList);
    }

    @Override
    public List<ClearingReconcileResult> queryBySettleOrderNoList(List<String> settleOrderNoList) {
        if (CollectionUtils.isEmpty(settleOrderNoList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ClearingReconcileResultPO> wrapper = Wrappers.<ClearingReconcileResultPO>lambdaQuery()
                .in(ClearingReconcileResultPO::getSettleOrderNo, settleOrderNoList)
                .orderByDesc(ClearingReconcileResultPO::getTransactionCompleteTime);
        List<ClearingReconcileResultPO> list = list(wrapper);
        return ClearingReconcileResultPOConverter.INSTANCE.toClearingReconcileResultList(list);
    }

    @Override
    public List<ClearingReconcileResult> queryByPageForCut(String settleOrderNo, String reconcileResult, Long lastId, Long pageSize) {
        List<ClearingReconcileResultPO> poList = clearingReconcileResultMapper.queryByPageForCut(settleOrderNo, reconcileResult, lastId, pageSize);
        return ClearingReconcileResultPOConverter.INSTANCE.toClearingReconcileResultList(poList);
    }

    @Override
    public List<ClearingReconcileResult> queryByOldKeyList2(String batchNo, List<String> oldKeyList) {
        if (CollectionUtils.isEmpty(oldKeyList)) {
            return Lists.newArrayList();
        }
        List<ClearingReconcileResultPO> poList = clearingReconcileResultMapper.queryByOldKeyList2(batchNo, oldKeyList);
        return ClearingReconcileResultPOConverter.INSTANCE.toClearingReconcileResultList(poList);
    }

    @Override
    public Page<ClearingReconcileResult> findListByPage(ClearingReconcileResultCondition condition, Integer pageNum, Integer pageSize) {
        BizUtil.startPage(pageNum, pageSize, "create_time");
        List<ClearingReconcileResultPO> list = this.findList(condition);
        PageInfo<ClearingReconcileResultPO> pageInfo = new PageInfo<>(list);
        return BizUtil.convertPage(pageInfo, po -> ClearingReconcileResultPOConverter.INSTANCE.toClearingReconcileResult(po));
    }

    @Override
    public void updateInternalOrderNo(List<ClearingReconcileResult> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }

        List<ClearingReconcileResultPO> poList = list.stream().map(v -> {
            ClearingReconcileResultPO po = new ClearingReconcileResultPO();
            po.setId(v.getId());
            po.setInternalOrderNo(v.getInternalOrderNo());
            return po;
        }).collect(Collectors.toList());
        clearingReconcileResultMapper.updateInternalOrderNo(poList);
    }

    @Override
    public void updateForSecondRecon(List<Long> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return;
        }

        clearingReconcileResultMapper.updateForSecondRecon(idList);
    }

    @Override
    public void updateForLaterRecon(List<String> resultIdList) {
        if (CollectionUtil.isEmpty(resultIdList)) {
            return;
        }

        clearingReconcileResultMapper.updateForLaterRecon(resultIdList);
    }

    @Override
    public ClearingReconcileTotalResult queryTransactionCompleteTimeRange(String batchNo) {
        return clearingReconcileResultMapper.queryTransactionCompleteTimeRange(batchNo);
    }

    @Override
    public void writeArrivedSettleMsg(List<ClearingReconcileResult> list) {
        if (CollectionUtil.isEmpty(list)){
            return;
        }
        clearingReconcileResultMapper.batchUpdateArriveSettleMsg(ClearingReconcileResultPOConverter.INSTANCE.toClearingReconcileResultPOList(list));
    }

    @Override
    public int queryCountBySettleOrderNoList(List<String> settleOrderNoList) {
        if (CollectionUtil.isEmpty(settleOrderNoList)) {
            return 0;
        }
        return clearingReconcileResultMapper.queryCountBySettleOrderNoList(settleOrderNoList);
    }

    @Override
    public List<ClearingReconcileResult> querySettleMsgBySettleOrderNoList(List<String> settleOrderNoList, Integer pageNum, Integer pageSize) {
        if (CollectionUtils.isEmpty(settleOrderNoList)) {
            return Collections.emptyList();
        }
        List<ClearingReconcileResultPO> list = clearingReconcileResultMapper.querySettleMsgBySettleOrderNoList(settleOrderNoList, pageNum * pageSize, pageSize);
        return ClearingReconcileResultPOConverter.INSTANCE.toClearingReconcileResultList(list);
    }

}
