package com.payermax.funds.reconcile.core.infrastructure.repository.converter;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.payermax.funds.reconcile.core.common.enums.BizTypeEnum;
import com.payermax.funds.reconcile.core.domain.domain.BatchResultAmount;
import com.payermax.funds.reconcile.core.domain.domain.BatchResultCount;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingReconcileResult;
import com.payermax.funds.reconcile.core.infrastructure.repository.entity.BatchResultAmountPO;
import com.payermax.funds.reconcile.core.infrastructure.repository.entity.BatchResultCountPO;
import com.payermax.funds.reconcile.core.infrastructure.repository.entity.ClearingReconcileResultPO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", imports = {BizTypeEnum.class, StringUtils.class, JSON.class, JSONObject.class})
public interface ClearingReconcileResultPOConverter {

    ClearingReconcileResultPOConverter INSTANCE = Mappers.getMapper(ClearingReconcileResultPOConverter.class);

    @Mappings({
            @Mapping(target = "bizType", expression = "java(BizTypeEnum.getByCode(clearingReconcileResultPO.getBizType()))")
    })
    ClearingReconcileResult toClearingReconcileResult(ClearingReconcileResultPO clearingReconcileResultPO);

    List<ClearingReconcileResult> toClearingReconcileResultList(List<ClearingReconcileResultPO> clearingReconcileResultPOList);

    @Mappings({
            @Mapping(target = "bizType", expression = "java(clearingReconcileResult.getExchangeType() != null ? clearingReconcileResult.getBizType().getCode() : null)")
    })
    ClearingReconcileResultPO toClearingReconcileResultPO(ClearingReconcileResult clearingReconcileResult);

    List<ClearingReconcileResultPO> toClearingReconcileResultPOList(List<ClearingReconcileResult> clearingReconcileResultList);

    default List<BatchResultCount> toBatchResultCount(List<BatchResultCountPO> list) {

        List<BatchResultCount> resultCountList = new ArrayList<>();

        if (CollectionUtils.isEmpty(list)) {
            return resultCountList;
        }

        return list.stream().map(v -> {
            return BatchResultCount.builder()
                    .count(v.getCount())
                    .reconcileResult(v.getReconcileResult())
                    .build();
        }).collect(Collectors.toList());
    }

    BatchResultAmount toBatchResultAmount(BatchResultAmountPO po);

    List<BatchResultAmount> toBatchResultAmountList(List<BatchResultAmountPO> poList);
}
