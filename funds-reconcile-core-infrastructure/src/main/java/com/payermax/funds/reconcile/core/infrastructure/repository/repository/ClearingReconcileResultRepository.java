package com.payermax.funds.reconcile.core.infrastructure.repository.repository;

import com.payermax.common.lang.model.dto.Page;
import com.payermax.funds.reconcile.core.domain.domain.BatchResultAmount;
import com.payermax.funds.reconcile.core.domain.domain.BatchResultCount;
import com.payermax.funds.reconcile.core.domain.domain.ClearingReconcileTotalResult;
import com.payermax.funds.reconcile.core.domain.domain.ReconcileBatchResult;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingReconcileResult;
import com.payermax.funds.reconcile.core.infrastructure.repository.condition.ClearingReconcileResultCondition;

import java.util.List;

/**
 * <p>
 * 清算对账结果 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ClearingReconcileResultRepository {

    void saveBatch(List<ClearingReconcileResult> dataList);

    void updateBatchById(List<ClearingReconcileResult> dataList);

    List<BatchResultCount> statistics(String batchNo);

    ReconcileBatchResult timeStatistics(String batchNo);

    List<BatchResultAmount> amountStatistics(String batchNo);

    void invalidReconResult(String batchNo);

    int queryCount(String batchNo, Boolean forSettleOrderNo, List<String> reconcileResultList);

    List<ClearingReconcileResult> queryByPage(String batchNo, Integer page, Integer pageSize, Boolean forSettleOrderNo, List<String> reconcileResult);

    List<ClearingReconcileResult> queryByOldKeyList(String batchNo, List<String> oldKeyList);

    void updateSettleOrderNo(String settleOrderNo, String batchNo, List<String> resultIdList);

    int queryCountBySettleOrderNo(String settleOrderNo, String reconcileResult);

    List<ClearingReconcileResult> queryBySettleOrderNoPage(String settleOrderNo, String reconcileResult, Integer page, Integer pageSize);

    List<ClearingReconcileResult> queryBySettleOrderNoList(List<String> settleOrderNoList);

    List<ClearingReconcileResult> queryByPageForCut(String settleOrderNo, String reconcileResult, Long lastId, Long pageSize);

    List<ClearingReconcileResult> queryByOldKeyList2(String batchNo, List<String> oldKeyList);

    Page<ClearingReconcileResult> findListByPage(ClearingReconcileResultCondition condition, Integer pageNum, Integer pageSize);

    void updateInternalOrderNo(List<ClearingReconcileResult> list);

    void updateForSecondRecon(List<Long> idList);

    void updateForLaterRecon(List<String> resultIdList);

    ClearingReconcileTotalResult queryTransactionCompleteTimeRange(String batchNo);

    /**
     * 写入已到账的结算信息
     */
    void writeArrivedSettleMsg(List<ClearingReconcileResult> list);

    int queryCountBySettleOrderNoList(List<String> settleOrderNoList);

    List<ClearingReconcileResult> querySettleMsgBySettleOrderNoList(List<String> settleOrderNoList, Integer pageNum, Integer pageSize);
}
