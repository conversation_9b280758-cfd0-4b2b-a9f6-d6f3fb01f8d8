package com.payermax.funds.reconcile.core.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.payermax.funds.reconcile.core.domain.domain.ClearingReconcileTotalResult;
import com.payermax.funds.reconcile.core.infrastructure.repository.entity.BatchResultAmountPO;
import com.payermax.funds.reconcile.core.infrastructure.repository.entity.BatchResultCountPO;
import com.payermax.funds.reconcile.core.infrastructure.repository.entity.ClearingReconcileResultPO;
import com.payermax.funds.reconcile.core.infrastructure.repository.entity.ReconcileBatchResultPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 清算对账结果 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ClearingReconcileResultMapper extends BaseMapper<ClearingReconcileResultPO> {

    List<BatchResultCountPO> statistics(@Param("batchNo") String batchNo);

    ReconcileBatchResultPO timeStatistics(@Param("batchNo") String batchNo);

    List<BatchResultAmountPO> amountStatistics(@Param("batchNo") String batchNo);

    int invalidReconResult(@Param("batchNo") String batchNo);

    int queryCount(@Param("batchNo") String batchNo, @Param("forSettleOrderNo") Boolean forSettleOrderNo, @Param("reconcileResultList") List<String> reconcileResultList);

    List<ClearingReconcileResultPO> queryByPage(@Param("batchNo") String batchNo, @Param("start") Integer start, @Param("pageSize") Integer pageSize,
                                                @Param("forSettleOrderNo") Boolean forSettleOrderNo, @Param("reconcileResultList") List<String> reconcileResultList);

    List<ClearingReconcileResultPO> queryByOldKeyList(@Param("batchNo") String batchNo, @Param("oldKeyList") List<String> oldKeyList);

    int updateSettleOrderNo(@Param("settleOrderNo") String settleOrderNo, @Param("batchNo") String batchNo, @Param("resultIdList") List<String> resultIdList);

    List<ClearingReconcileResultPO> queryBySettleOrderNoPage(@Param("settleOrderNo") String settleOrderNo, @Param("reconcileResult") String reconcileResult, @Param("start") Integer start, @Param("pageSize") Integer pageSize);

    List<ClearingReconcileResultPO> queryByPageForCut(@Param("settleOrderNo") String settleOrderNo, @Param("reconcileResult") String reconcileResult, @Param("lastId") Long lastId, @Param("pageSize") Long pageSize);

    List<ClearingReconcileResultPO> queryByOldKeyList2(@Param("batchNo") String batchNo, @Param("oldKeyList") List<String> oldKeyList);

    int updateInternalOrderNo(@Param("list") List<ClearingReconcileResultPO> list);

    int updateForSecondRecon(@Param("idList") List<Long> idList);

    int updateForLaterRecon(@Param("resultIdList") List<String> resultIdList);

    ClearingReconcileTotalResult queryTransactionCompleteTimeRange(@Param("batchNo") String batchNo);

    int batchUpdateArriveSettleMsg(@Param("list") List<ClearingReconcileResultPO> list);

    int queryCountBySettleOrderNoList(@Param("settleOrderNoList") List<String> settleOrderNoList);

    List<ClearingReconcileResultPO> querySettleMsgBySettleOrderNoList(@Param("settleOrderNoList") List<String> settleOrderNoList, @Param("pageNum")Integer pageNum,@Param("pageSize") Integer pageSize );

}
