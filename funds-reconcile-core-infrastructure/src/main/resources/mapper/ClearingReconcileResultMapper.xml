<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.funds.reconcile.core.infrastructure.repository.mapper.ClearingReconcileResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.payermax.funds.reconcile.core.infrastructure.repository.entity.ClearingReconcileResultPO">
        <id column="id" property="id"/>
        <result column="result_id" property="resultId"/>
        <result column="batch_no" property="batchNo"/>
        <result column="uuid" property="uuid"/>
        <result column="technical_org" property="technicalOrg"/>
        <result column="biz_handle_org" property="bizHandleOrg"/>
        <result column="funds_settle_org" property="fundsSettleOrg"/>
        <result column="entity" property="entity"/>
        <result column="funds_account_id" property="fundsAccountId"/>
        <result column="biz_type" property="bizType"/>
        <result column="internal_order_no" property="internalOrderNo"/>
        <result column="third_org_order_no" property="thirdOrgOrderNo"/>
        <result column="fourth_org_order_no" property="fourthOrgOrderNo"/>
        <result column="quote_internal_order_no" property="quoteInternalOrderNo"/>
        <result column="quote_third_org_order_no" property="quoteThirdOrgOrderNo"/>
        <result column="quote_fourth_org_order_no" property="quoteFourthOrgOrderNo"/>
        <result column="voucher_type" property="voucherType"/>
        <result column="voucher_no" property="voucherNo"/>
        <result column="in_transaction_currency" property="inTransactionCurrency"/>
        <result column="in_transaction_amount" property="inTransactionAmount"/>
        <result column="transaction_currency" property="transactionCurrency"/>
        <result column="transaction_amount" property="transactionAmount"/>
        <result column="settle_currency" property="settleCurrency"/>
        <result column="settle_amount" property="settleAmount"/>
        <result column="fee_info" property="feeInfo"/>
        <result column="pay_direction" property="payDirection"/>
        <result column="exchange_type" property="exchangeType"/>
        <result column="exchange_rate" property="exchangeRate"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="out_mid" property="outMid"/>
        <result column="transaction_create_time" property="transactionCreateTime"/>
        <result column="transaction_complete_time" property="transactionCompleteTime"/>
        <result column="settle_time" property="settleTime"/>
        <result column="other_fields" property="otherFields"/>
        <result column="order_status" property="orderStatus"/>
        <result column="reconcile_result" property="reconcileResult"/>
        <result column="deduct_fee_method" property="deductFeeMethod"/>
        <result column="settle_order_no" property="settleOrderNo"/>
        <result column="remark" property="remark"/>
        <result column="owner" property="owner"/>
        <result column="diff_status" property="diffStatus"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_time" property="createTime"/>
        <result column="creator" property="creator"/>
        <result column="modified_time" property="modifiedTime"/>
        <result column="modifier" property="modifier"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, result_id, batch_no, uuid, technical_org, biz_handle_org, funds_settle_org, entity, funds_account_id, biz_type, internal_order_no, third_org_order_no, fourth_org_order_no, quote_internal_order_no, quote_third_org_order_no, quote_fourth_org_order_no, voucher_type, voucher_no, in_transaction_currency, in_transaction_amount, transaction_currency, transaction_amount, settle_currency, settle_amount, fee_info, pay_direction, exchange_type, exchange_rate, payment_method, out_mid, transaction_create_time, transaction_complete_time, settle_time, other_fields, order_status, reconcile_result, deduct_fee_method, settle_order_no, remark, owner, diff_status, is_deleted, create_time, creator, modified_time, modifier
    </sql>

    <select id="statistics" parameterType="java.lang.String"
            resultType="com.payermax.funds.reconcile.core.infrastructure.repository.entity.BatchResultCountPO">
        select
        reconcile_result,count(1) as count
        from tb_clearing_reconcile_result
        where batch_no = #{batchNo}
        group by reconcile_result
    </select>

    <select id="timeStatistics" parameterType="java.lang.String"
            resultType="com.payermax.funds.reconcile.core.infrastructure.repository.entity.ReconcileBatchResultPO">
        SELECT
        batch_no AS batchNo, MIN(transaction_complete_time) AS orderStartTime, MAX(transaction_complete_time) AS orderEndTime
        FROM tb_clearing_reconcile_result
        WHERE batch_no = #{batchNo}
    </select>

    <select id="amountStatistics" parameterType="java.lang.String"
            resultType="com.payermax.funds.reconcile.core.infrastructure.repository.entity.BatchResultAmountPO">
        SELECT
        biz_type as bizType,
        transaction_currency as transactionCurrency,
        settle_currency as settleCurrency,
        count(1) as totalNum,
        sum(transaction_amount) as totalTransactionAmount,
        sum(settle_amount) as totalSettleAmount,
        count(case when reconcile_result='reconciled' then 1 else null end) as reconciledCount,
        sum(case when reconcile_result='reconciled' then transaction_amount else 0 end) as reconciledAmount,
        count(case when reconcile_result!='reconciled' then 1 else null end) as diffCount,
        sum(case when reconcile_result!='reconciled' then transaction_amount else 0 end) as diffAmount
        FROM tb_clearing_reconcile_result
        WHERE batch_no = #{batchNo}
        GROUP BY biz_type,transaction_currency,settle_currency;
    </select>

    <update id="invalidReconResult">
        update tb_clearing_reconcile_result
        set is_deleted= 1
        where batch_no= #{batchNo}
        and is_deleted= 0
    </update>

    <select id="queryCount" resultType="java.lang.Integer">
        select
        count(1)
        from tb_clearing_reconcile_result
        where batch_no=#{batchNo}
        <if test="forSettleOrderNo != null and forSettleOrderNo">
            and (settle_order_no is null or settle_order_no = '')
        </if>
        <if test="reconcileResultList != null and reconcileResultList.size()>0">
            and reconcile_result in
            <foreach collection="reconcileResultList" item="reconcileResult"
                     index="index" open="(" close=")" separator=",">
                #{reconcileResult}
            </foreach>
        </if>
        and is_deleted = 0
    </select>

    <select id="queryByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_clearing_reconcile_result
        where batch_no=#{batchNo}
        <if test="forSettleOrderNo != null and forSettleOrderNo">
            and (settle_order_no is null or settle_order_no = '')
        </if>
        <if test="reconcileResultList != null and reconcileResultList.size()>0">
            and reconcile_result in
            <foreach collection="reconcileResultList" item="reconcileResult"
                     index="index" open="(" close=")" separator=",">
                #{reconcileResult}
            </foreach>
        </if>
        and is_deleted = 0
        order by id
        limit #{start}, #{pageSize}
    </select>

    <select id="queryByOldKeyList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_clearing_reconcile_result
        where batch_no=#{batchNo}
        and is_deleted = 0
        and CONCAT(
        ifnull(internal_order_no,'null'),'_',ifnull(third_org_order_no,'null'),'_',ifnull(fourth_org_order_no,'null'),'_',
        ifnull(quote_internal_order_no,'null'),'_',ifnull(quote_third_org_order_no,'null'),'_',ifnull(quote_fourth_org_order_no,'null')
        )
        in
        <foreach collection="oldKeyList" item="oldKey"
                 index="index" open="(" close=")" separator=",">
            #{oldKey}
        </foreach>
    </select>

    <update id="updateSettleOrderNo">
        update tb_clearing_reconcile_result
        set settle_order_no = #{settleOrderNo}
        where batch_no= #{batchNo}
        and result_id in
        <foreach collection="resultIdList" item="resultId"
                 index="index" open="(" close=")" separator=",">
            #{resultId}
        </foreach>
        and is_deleted = 0
    </update>

    <select id="queryBySettleOrderNoPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_clearing_reconcile_result
        where settle_order_no=#{settleOrderNo}
        <if test="reconcileResult != null and reconcileResult != ''">
            and reconcile_result=#{reconcileResult}
        </if>
        and is_deleted = 0
        order by id
        limit #{start}, #{pageSize}
    </select>

    <select id="queryByPageForCut" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_clearing_reconcile_result
        where settle_order_no=#{settleOrderNo}
        and reconcile_result=#{reconcileResult}
        and id &gt; #{lastId}
        order by id
        limit #{pageSize}
    </select>

    <select id="queryByOldKeyList2" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_clearing_reconcile_result
        where batch_no=#{batchNo}
        and is_deleted = 0
        and
        (result_id in
        <foreach collection="oldKeyList" item="oldKey"
                 index="index" open="(" close=")" separator=",">
            #{oldKey}
        </foreach>
        or
        internal_order_no in
        <foreach collection="oldKeyList" item="oldKey"
                 index="index" open="(" close=")" separator=",">
            #{oldKey}
        </foreach>
        )
    </select>

    <update id="updateInternalOrderNo">
        update tb_clearing_reconcile_result set
        internal_order_no = case id
        <foreach collection="list" item="item">
            when #{item.id} then #{item.internalOrderNo}
        </foreach>
        end
        where id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
    </update>

    <update id="updateForSecondRecon">
        update tb_clearing_reconcile_result
        set reconcile_result = 'reconciled'
        where id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updateForLaterRecon">
        update tb_clearing_reconcile_result
        set diff_status = 'SUCCESS'
        where result_id in
        <foreach collection="resultIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="queryTransactionCompleteTimeRange"
            resultType="com.payermax.funds.reconcile.core.domain.domain.ClearingReconcileTotalResult">
        select
        batch_no, min(transaction_complete_time) transactionCompleteTimeStart, max(transaction_complete_time) transactionCompleteTimeEnd
        from tb_clearing_reconcile_result
        where batch_no=#{batchNo}
        and biz_type in ('receipt','refund')
        and reconcile_result = 'diff_external_single'
        and is_deleted = 0
        group by batch_no
    </select>

    <update id="batchUpdateArriveSettleMsg">
        UPDATE tb_clearing_reconcile_result
        <trim prefix="SET" suffixOverrides=",">
            <!-- 更新到账币种 -->
            <trim prefix="arrived_settle_currency = CASE" suffix="END,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.arrivedSettleCurrency != null">
                        WHEN result_id = #{item.resultId} THEN #{item.arrivedSettleCurrency}
                    </if>
                </foreach>
            </trim>

            <!-- 更新到账金额 -->
            <trim prefix="arrived_settle_amount = CASE" suffix="END,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.arrivedSettleAmount != null">
                        WHEN result_id = #{item.resultId} THEN #{item.arrivedSettleAmount}
                    </if>
                </foreach>
            </trim>

            <!-- 更新到账时间 -->
            <trim prefix="arrived_settle_time = CASE" suffix="END,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.arrivedSettleTime != null">
                        WHEN result_id = #{item.resultId} THEN #{item.arrivedSettleTime}
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE result_id IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.resultId}
        </foreach>
    </update>


    <select id="queryCountBySettleOrderNoList" resultType="java.lang.Integer">
        select
            count(1)
        from tb_clearing_reconcile_result
        where settle_order_no in
        <if test="settleOrderNoList != null and settleOrderNoList.size()>0">
            <foreach collection="settleOrderNoList" item="settleOrderNo" index="index" open="(" close=")" separator=",">
                #{settleOrderNo}
            </foreach>
        </if>
        and is_deleted = 0
    </select>

    <select id="querySettleMsgBySettleOrderNoList" resultMap="BaseResultMap">
        select
            id, result_id, settle_currency, settle_amount, settle_time, settle_order_no
        from tb_clearing_reconcile_result
        where settle_order_no in
        <if test="settleOrderNoList != null and settleOrderNoList.size()>0">
            <foreach collection="settleOrderNoList" item="settleOrderNo" index="index" open="(" close=")" separator=",">
                #{settleOrderNo}
            </foreach>
        </if>
        and is_deleted = 0
        order by id
        limit #{pageNum}, #{pageSize}
    </select>
</mapper>
