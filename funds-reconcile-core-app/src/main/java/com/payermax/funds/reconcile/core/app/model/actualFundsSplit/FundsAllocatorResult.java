package com.payermax.funds.reconcile.core.app.model.actualFundsSplit;

import com.payermax.funds.reconcile.core.domain.domain.ActualFundsAllocateDetail;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingReconcileResult;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingSettleOrder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/20
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FundsAllocatorResult extends ClearingReconcileResult{

    /**
     * 关联结算单
     */
    private ClearingSettleOrder clearingSettleOrder;

    private String settleCurrency;

    private BigDecimal expectSettleAmount;

    private BigDecimal arrivedSettleAmount;

    private String arrivedSettleCurrency;

    private Date arrivedSettleTime;

    private BigDecimal unarrivedAmount;

    private BigDecimal unarrivedAmountWeight;

    private  List<ActualFundsAllocateDetail> allocatorDetails;

    private Boolean newly = true;
}
