package com.payermax.funds.reconcile.core.app.service.allocate.handler;

import com.payermax.common.lang.exception.BusinessException;
import com.payermax.funds.reconcile.core.app.converter.ActualFundsAllocatorConvert;
import com.payermax.funds.reconcile.core.app.model.actualFundsSplit.ActualFundsAllocatorContext;
import com.payermax.funds.reconcile.core.app.model.actualFundsSplit.FundsAllocatorResult;
import com.payermax.funds.reconcile.core.common.enums.BankPayDirectionEnum;
import com.payermax.funds.reconcile.core.common.enums.ReconErrorCode;
import com.payermax.funds.reconcile.core.common.utils.AssertUtil;
import com.payermax.funds.reconcile.core.common.utils.CalculateUtils;
import com.payermax.funds.reconcile.core.domain.domain.ActualFundsAllocateDetail;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingReconcileResult;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingSettleOrder;
import com.payermax.funds.reconcile.core.domain.domain.funds.FundsReconcileRelationItem;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.FundsAllocateDetailRepository;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.FundsReconcileRelationItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ActualFundsAllocator {

    private final FundsReconcileRelationItemRepository reconcileRelationItemRepository;
    private final FundsAllocateDetailRepository allocateDetailRepository;
    private final ActualFundsAllocatorConvert convert;
    private final ActualFundsAllocateCommonUtils commonUtils;


    /**
     * 拆分计算
     */
    public void allocate(ActualFundsAllocatorContext context) {
        log.info("开始拆分计算...");

        List<FundsAllocatorResult> fundsAllocatorResultList = new ArrayList<>();
        // 分配当前轮次关联
        List<FundsAllocatorResult> currentAllocateResultList = allocateCurrentRelation(context);

        // 分配销账关联
        Map<String, List<FundsAllocatorResult>> clearReconWeightMap = new HashMap<>();
        List<FundsAllocatorResult> chargeOffShortResultList = context.getChargeOffShortList().stream()
                .flatMap(item -> allocateChargeOffShort(context, item, clearReconWeightMap).stream())
                .collect(Collectors.toList());

        fundsAllocatorResultList.addAll(currentAllocateResultList);
        fundsAllocatorResultList.addAll(chargeOffShortResultList);
        // 计算最终到账金额
        String relationNo = context.getRelation().getRelationNo();
        log.info("开始计算最终到账金额...");
        List<List<FundsAllocatorResult>> partition = commonUtils.commonPartition(fundsAllocatorResultList);
        partition.forEach(allocatorResults -> {
            // 查询拆分明细
            List<String> resultIdList = allocatorResults.stream().map(FundsAllocatorResult::getResultId).collect(Collectors.toList());
            Map<String, List<ActualFundsAllocateDetail>> resultDetailMap = allocateDetailRepository.findByClearingReconResultIdList(resultIdList);
            Map<String, Set<String>> relationNoSetMap = resultDetailMap.entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                            .map(ActualFundsAllocateDetail::getSourceRelationNo).filter(Objects::nonNull).collect(Collectors.toSet())));

            allocatorResults.forEach(item -> {
                // 判断本关联是否已被分配
                boolean isAllocated = relationNoSetMap.getOrDefault(item.getResultId(), Collections.emptySet()).contains(relationNo);
                // 未被分配则汇总前序其他分配明细进行计算
                if (isAllocated) {
                    log.info("{} 已被 {} 分配，跳过...", item.getResultId(), relationNo);
                    item.setNewly(false);
                }else{
                    List<ActualFundsAllocateDetail> allocatedDetailList = resultDetailMap.getOrDefault(relationNo, new ArrayList<>());
                    allocatedDetailList.addAll(Optional.ofNullable(item.getAllocatorDetails()).orElse(Collections.emptyList()));
                    Date maxSettleTime = item.getAllocatorDetails().stream().map(ActualFundsAllocateDetail::getSettleTime).max(Date::compareTo).orElseGet(Date::new);
                    item.setArrivedSettleAmount(allocatedDetailList.stream().map(ActualFundsAllocateDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    item.setSettleTime(maxSettleTime);
                }
            });
        });

        // --- 保存或处理拆分结果 ---
        log.info("RelationNo: {} 拆分计算完成。准备保存结果...", context.getRelation().getRelationNo());

        BigDecimal finalTotalArrivedAmount = fundsAllocatorResultList.stream()
                .map(FundsAllocatorResult::getAllocatorDetails).flatMap(Collection::stream).map(ActualFundsAllocateDetail::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("银行支付[{}] - 银行收费[{}] - 长款挂账 [{}] + 长款销账 [{}]",
                context.getTotalInstPaymentAmount(), context.getTotalInstChargeAmount(), context.getTotalChargeOnLongAmount(), context.getTotalChargeOffLongAmount());
        log.info("预期到账(未扣费)[{}] - 短款挂账[{}] = 最终拆分到账: [{}]", context.getTotalExpectSettleAmount(), context.getTotalChargeOnShortAmount(), finalTotalArrivedAmount);

        context.setFundsAllocatorResultList(fundsAllocatorResultList);
    }


    /**
     * 分摊当前关联的流水
     */
    private List<FundsAllocatorResult> allocateCurrentRelation(ActualFundsAllocatorContext context) {
        String settleCurrency = context.getSettleCurrency();
        // 实际结算金额 = 总结算金额 - 短款销账金额
        BigDecimal totalSettleAmount = context.getTotalSettleAmount().subtract(context.getTotalChargeOffShortAmount());
        BigDecimal settleOrderAmount = context.getCurrentRelationSettleAmount();
        BigDecimal chargeOnShortAmount = context.getTotalChargeOnShortAmount();
        BigDecimal settleAmount = settleOrderAmount.subtract(chargeOnShortAmount);
        // 本轮结算金额占比
        BigDecimal currentRelationWeight = commonUtils.divide(settleAmount, totalSettleAmount);
        List<FundsAllocatorResult> allocatorResults = commonUtils.getAllocateResultBySettleOrder(context.getOurSettleOrderList());
        // 用于获取结算单对应的 Item，key: biz_no
        Map<String, FundsReconcileRelationItem> ourSettleOrderItemMap = commonUtils.getRelationItemSettleOrderMap(context.getOurSettleOrderItemList());
        // 计算每笔流水的权重
        allocatorResults.forEach(item -> {
            BigDecimal unarrivedAmountWeight = getClearingReconWeight(item, ourSettleOrderItemMap, settleCurrency, settleOrderAmount);
            BigDecimal finalWeight = commonUtils.multiply(currentRelationWeight, unarrivedAmountWeight);
            item.setUnarrivedAmountWeight(finalWeight);
            log.info("外部流水权重计算 => 本轮结算金额权重[{}] * 本流水未到账金额权重[{}] = 最终权重[{}]", currentRelationWeight, unarrivedAmountWeight, finalWeight);
        });

        // 分配银行流水金额
        log.info("开始分配银行流水金额...");
        allocateInstBill(context, allocatorResults);

        // 分配长款销账金额
        log.info("开始分配长款销账金额...");
        context.getChargeOffLongList().forEach(item -> {
            // 当找不到来源的银行流水时，直接使用本笔销账记录
            FundsReconcileRelationItem fundsReconcileRelationItem = context.getChargeOffLongOriginItemMap().getOrDefault(item.getBizNo(), item);
            allocateByWeight(item, fundsReconcileRelationItem, allocatorResults, false, "[长款拆分]");
        });
        return allocatorResults;
    }

    /**
     * 分配短款销账关联的流水
     */
    private List<FundsAllocatorResult> allocateChargeOffShort(ActualFundsAllocatorContext context, FundsReconcileRelationItem relationItem, Map<String, List<FundsAllocatorResult>> clearReconWeightMap) {
        FundsReconcileRelationItem originRelationItem = reconcileRelationItemRepository.findByBizNo(relationItem.getQuotationBizNo());
        // 计算短款销账对应的流水权重
        String settleCurrency = context.getSettleCurrency();
        List<FundsAllocatorResult> allocatorResultList;

        // 已计算过则直接使用权重
        if (clearReconWeightMap.containsKey(originRelationItem.getRelationNo())){
            allocatorResultList = convert.deepCopyList(clearReconWeightMap.get(originRelationItem.getRelationNo()));
        }else {
            // 来源关联、结算单、外部流水
            List<FundsReconcileRelationItem> originRelationList = reconcileRelationItemRepository.findByRelationNo(originRelationItem.getRelationNo());
            List<ClearingSettleOrder> clearingSettleOrderList = commonUtils.getSettleOrderByRelationList(originRelationList);
            List<FundsAllocatorResult> allocatorResults = commonUtils.getAllocateResultBySettleOrder(clearingSettleOrderList);
            // 用于获取结算单对应的 Item，key: biz_no
            Map<String, FundsReconcileRelationItem> ourSettleOrderItemMap = commonUtils.getRelationItemSettleOrderMap(originRelationList);
            // 结算单总金额
            BigDecimal settleOrderAmount = CalculateUtils.sumListAmount(clearingSettleOrderList, ClearingSettleOrder::getSettleAmount);
            // 计算每笔流水的权重
            allocatorResults.forEach(item -> {
                BigDecimal amountWeight = getClearingReconWeight(item, ourSettleOrderItemMap, settleCurrency, settleOrderAmount);
                item.setUnarrivedAmountWeight(amountWeight);
            });
            clearReconWeightMap.put(originRelationItem.getRelationNo(), allocatorResults);
            allocatorResultList = convert.deepCopyList(allocatorResults);
        }
        allocatorResultList.forEach(item -> {
            log.info("短款销账权重计算 => 本轮结算金额权重[{}] * 流水权重[{}] = 短款销账权重[{}]", item.getUnarrivedAmountWeight(), item.getUnarrivedAmountWeight(), item.getUnarrivedAmountWeight());
        });
        allocateByWeight(relationItem, relationItem, allocatorResultList, false, "[短款销账拆分]");
        return allocatorResultList;
    }


    /**
     * 分配银行流水金额
     */
    private void allocateInstBill(ActualFundsAllocatorContext context, List<FundsAllocatorResult> fundsAllocatorResultList) {
        Map<BankPayDirectionEnum, List<FundsReconcileRelationItem>> instBillDirectionGroup = context.getInstBillDirectionGroup();
        Map<String, List<FundsReconcileRelationItem>> chargeOnLongItemGroup = context.getChargeOnLongItemGroup();
        
        // 银行应付
        instBillDirectionGroup.getOrDefault(BankPayDirectionEnum.CREDIT, Collections.emptyList()).stream()
                .peek(item -> {
                    BigDecimal amount = item.getAmount();
                    // 单个银行流水金额占比
                    BigDecimal weight = commonUtils.divide(item.getAmount(), context.getTotalInstPaymentAmount());
                    BigDecimal chargeOffShortAmount = weight.multiply(context.getTotalChargeOffShortAmount());
                    item.setAmount(amount.subtract(chargeOffShortAmount));
                    // 需要跟长款挂账进行轧差
                    item.setAmount(chargeOnLongNetting(item, chargeOnLongItemGroup));
                    log.info("银行流水金额[{}] = 银行流水金额[{}] - 短款销账金额[{}]", item.getAmount(), amount, chargeOffShortAmount);
                })
                .forEach(item -> allocateByWeight(item, item, fundsAllocatorResultList, isReversedDirection(item.getPayDirection()), "[银行流水正向拆分]"));
        // 银行应收
        instBillDirectionGroup.getOrDefault(BankPayDirectionEnum.DEBIT, Collections.emptyList()).forEach(item ->
                allocateByWeight(item, item, fundsAllocatorResultList, isReversedDirection(item.getPayDirection()), "[银行流水反向拆分]"));
    }

    /**
     * 长款挂账轧差
     */
    private BigDecimal chargeOnLongNetting(FundsReconcileRelationItem item, Map<String, List<FundsReconcileRelationItem>> chargeOnLongItemMap){
        BigDecimal netAmount = item.getAmount();
        // 长款挂账轧差
        List<FundsReconcileRelationItem> chargeLongItems = chargeOnLongItemMap.getOrDefault(item.getBizNo(), Collections.emptyList());
        BigDecimal chargeOnAmount = chargeLongItems.stream().map(FundsReconcileRelationItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 轧差金额 = 银行流水金额 - 长款挂账金额
        netAmount = netAmount.subtract(chargeOnAmount);
        log.info("银行流水: {}  银行流水金额[{}] - 长款挂账[{}] = 轧差后金额[{}]", item.getBizNo(), item.getAmount(), chargeOnAmount, netAmount);
        return netAmount;
    }


    /**
     * 获取外部流水换汇后金额权重
     */
    private BigDecimal getClearingReconWeight(FundsAllocatorResult result, Map<String, FundsReconcileRelationItem> settleItemMap, String settleCurrency, BigDecimal totalExpectSettleAmount){
        // 计算待结金额
        String currency = result.getSettleCurrency();
        BigDecimal finalAmount = result.getSettleAmount();
        // 是否需要换汇
        if (!currency.equalsIgnoreCase(settleCurrency)){
            String parentNo = result.getClearingSettleOrder().getParentNo();
            FundsReconcileRelationItem ourSettleOrderItem = Optional.ofNullable(settleItemMap.get(parentNo)).orElseThrow(() -> new BusinessException(ReconErrorCode.ACTUAL_FUNDS_ALLOCATE_CONTEXT_VALIDATE_ERROR.getCode(), String.format("内部结算单关联的关联项不存在:%s", parentNo)));
            AssertUtil.isTrue(Objects.nonNull(ourSettleOrderItem.getExchangeRate()), "结算换汇时，汇率不存在");
            // 结算换汇时币种 结算必定是 USD
            finalAmount = finalAmount.multiply(ourSettleOrderItem.getExchangeRate());
        }
        BigDecimal clearingReconWeight = commonUtils.divide(finalAmount, totalExpectSettleAmount);
        log.info("外部流水 {}, 应结金额[{}] / 总结算金额[{}] = 权重[{}]", result.getResultId(), finalAmount, totalExpectSettleAmount, clearingReconWeight);
        return clearingReconWeight;
    }

    /**
     * 获取外部流水换汇后金额权重
     */
    private BigDecimal getClearingReconWeight(ClearingReconcileResult clearingReconcileResults, Map<String, FundsReconcileRelationItem> ourSettleOrderItemMap,
                                                Map<String, ClearingSettleOrder> ourSettleOrderMap, String settleCurrency, BigDecimal totalExpectSettleAmount){
        // 计算待结金额
        BigDecimal settleAmount = clearingReconcileResults.getSettleAmount();
        BigDecimal arrivedAmount = clearingReconcileResults.getArrivedSettleAmount();
        BigDecimal unarrivedAmount = settleAmount.subtract(arrivedAmount);
        String currency = clearingReconcileResults.getSettleCurrency();
        BigDecimal finalAmount = unarrivedAmount;
        // 是否需要换汇
        if (!currency.equalsIgnoreCase(settleCurrency)){
            String settleOrderNo = clearingReconcileResults.getSettleOrderNo();
            AssertUtil.isTrue(ourSettleOrderMap.containsKey(settleOrderNo), "外部流水关联的内部结算单不存在");
            String parentNo = ourSettleOrderMap.get(settleOrderNo).getParentNo();
            AssertUtil.isTrue(ourSettleOrderItemMap.containsKey(parentNo), "内部结算单关联的关联项不存在");
            FundsReconcileRelationItem ourSettleOrderItem = ourSettleOrderItemMap.get(parentNo);
            AssertUtil.isTrue(Objects.nonNull(ourSettleOrderItem.getExchangeRate()), "结算换汇时，汇率不存在");
            // 结算换汇时币种 结算必定是 USD
            finalAmount = finalAmount.multiply(ourSettleOrderItem.getExchangeRate());
        }
        BigDecimal clearingReconWeight = finalAmount.divide(totalExpectSettleAmount, 8, RoundingMode.HALF_UP);
        log.info("外部流水 {}, 未结金额[{}] / 应结总金额[{}] = 权重[{}]", clearingReconcileResults.getResultId(), unarrivedAmount, totalExpectSettleAmount, clearingReconWeight);
        return clearingReconWeight;
    }



    /**
     * 根据方向判断是否需要反向计算 (CREDIT贷方为正，即需要支付给我方的钱，DEBIT借方为负)
     */
    private Boolean isReversedDirection(BankPayDirectionEnum payDirection) {
        return !BankPayDirectionEnum.CREDIT.equals(payDirection);
    }


    /**
     * 按权重分配金额的通用方法
     * @param relationItem 用于分配的关联项
     * @param sourceRelationItem  用于分配的关联项的来源关联项
     * @param fundsAllocatorResultList 参与分配的外部流水
     * @param isReversed 是否需要反向计算[-]，默认为false，正向计算[+]
     */
    private void allocateByWeight(FundsReconcileRelationItem relationItem, FundsReconcileRelationItem sourceRelationItem
            , List<FundsAllocatorResult> fundsAllocatorResultList, Boolean isReversed, String description) {
        BigDecimal totalAmount = relationItem.getAmount();
        // 列表为空 || 总金额为0
        if (CollectionUtils.isEmpty(fundsAllocatorResultList) || totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        // 总权重
        BigDecimal totalWeight = fundsAllocatorResultList.stream()
                .map(FundsAllocatorResult::getUnarrivedAmountWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if(totalWeight.compareTo(BigDecimal.ZERO) == 0){
            log.warn("分配的关联项: {}，总金额: {}，但目标列表总权重为0，无法分配。", relationItem.getBizNo(), totalAmount);
            return;
        }

        BigDecimal remainingAmount = totalAmount;
        int lastIndex = fundsAllocatorResultList.size() - 1;

        for (int i = 0; i < fundsAllocatorResultList.size(); i++) {
            FundsAllocatorResult item = fundsAllocatorResultList.get(i);
            BigDecimal allocatedAmount;
            BigDecimal weight = item.getUnarrivedAmountWeight();
            if (i < lastIndex) {
                // 按权重均分
                allocatedAmount = commonUtils.multiply(totalAmount, weight);
                remainingAmount = remainingAmount.subtract(allocatedAmount);
                log.info("权重分配 => 外部流水: {}，来源关联: {}，权重: {}，分配金额: {}", item.getResultId(), relationItem.getBizNo(), weight, allocatedAmount);
            } else {
                // 最后一个元素使用剩余金额，保证总额平账
                allocatedAmount = remainingAmount;
                log.info("剩余金额分配 => 外部流水: {}，来源关联: {}，权重: {}，分配金额: {}", item.getResultId(), relationItem.getBizNo(), weight, allocatedAmount);
            }
            // 银行反向交易
            if(isReversed){
                log.info("存在反向交易 {} {}", relationItem.getBizNo(), relationItem.getPayDirection());
                allocatedAmount = allocatedAmount.negate();
            }
            log.info("来源 item: {}, 总金额, 权重: {},  分配金额: {}", item.getResultId(), weight, allocatedAmount);
            // 构造明细，回填数据
            String detailDesc = String.format("%s 分配外部流水: %s，来源关联单号: %s，权重: %s，分配金额: %s", description, item.getResultId(), relationItem.getBizNo(), weight, allocatedAmount);
            ActualFundsAllocateDetail actualFundsAllocateDetail = ActualFundsAllocatorConvert.INSTANCE.buildAllocatorDetail(item, relationItem, sourceRelationItem, weight, allocatedAmount, detailDesc);
            List<ActualFundsAllocateDetail> allocatorDetails = Optional.ofNullable(item.getAllocatorDetails()).orElseGet(ArrayList::new);
            allocatorDetails.add(actualFundsAllocateDetail);
            item.setAllocatorDetails(allocatorDetails);
        }
    }
}
