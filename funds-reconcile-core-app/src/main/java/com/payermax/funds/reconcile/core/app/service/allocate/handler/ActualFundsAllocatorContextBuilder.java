package com.payermax.funds.reconcile.core.app.service.allocate.handler;

import com.google.common.collect.Lists;
import com.payermax.funds.reconcile.core.app.model.actualFundsSplit.ActualFundsAllocatorContext;
import com.payermax.funds.reconcile.core.common.enums.BankPayDirectionEnum;
import com.payermax.funds.reconcile.core.common.enums.ReconErrorCode;
import com.payermax.funds.reconcile.core.common.enums.RelationItemTypeEnum;
import com.payermax.funds.reconcile.core.common.enums.SettleTradeOrderRelationFeeEnum;
import com.payermax.funds.reconcile.core.common.utils.AssertUtil;
import com.payermax.funds.reconcile.core.common.utils.CalculateUtils;
import com.payermax.funds.reconcile.core.common.utils.ExceptionUtils;
import com.payermax.funds.reconcile.core.domain.domain.ActualFundsAllocateDetail;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingReconcileResult;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingSettleOrder;
import com.payermax.funds.reconcile.core.domain.domain.funds.FundsReconcileRelation;
import com.payermax.funds.reconcile.core.domain.domain.funds.FundsReconcileRelationItem;
import com.payermax.funds.reconcile.core.infrastructure.repository.condition.ClearingSettleOrderCondition;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.ClearingSettleOrderRepository;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.FundsAllocateDetailRepository;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.FundsReconcileRelationItemRepository;
import com.ushareit.fintech.payrecon.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ActualFundsAllocatorContextBuilder {

    private final FundsReconcileRelationItemRepository reconcileRelationItemRepository;
    private final ClearingSettleOrderRepository clearingSettleOrderRepository;
    private final FundsAllocateDetailRepository allocateDetailRepository;

    /**
     * 构建上下文
     */
    public ActualFundsAllocatorContext build(FundsReconcileRelation relation) {

        log.info("开始构建上下文...");
        ActualFundsAllocatorContext context = new ActualFundsAllocatorContext().setRelation(relation);
        List<FundsReconcileRelationItem> relationItems = reconcileRelationItemRepository.findByRelationNo(relation.getRelationNo());
        context.setRelationItemList(relationItems);

        buildContextOriginData(context);

        buildContextAmountData(context);

        contextCheck(context);

        log.info("上下文构建完成: 银行流水条数: {}, 内部结算单条数: {}, 预期结算金额: {}", context.getInstBillList().size(), context.getOurSettleOrderList().size(), context.getTotalExpectSettleAmount());

        return context;

    }


    /**
     * 上下文检查
     */
    private void contextCheck(ActualFundsAllocatorContext context) {
        log.info("开始上下文校验...");
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(context.getInstBillList()), "无银行流水");
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(context.getOurSettleOrderList()), "无内部结算单");

        Set<String> instBillCcySet = context.getInstBillList().stream().map(FundsReconcileRelationItem::getCurrency).collect(Collectors.toSet());
        AssertUtil.isTrue(instBillCcySet.size() == 1, "银行流水币种不一致/不存在");

        Set<String> ourSettleCctSet = context.getOurSettleOrderList().stream().map(ClearingSettleOrder::getSettleCurrency).collect(Collectors.toSet());
        // 如果外部流水涉及多种货币，银行流水必须是某种锚定货币（如USD），这是常见的换汇结算场景
        if (ourSettleCctSet.size() >= 2) {
            AssertUtil.isTrue(instBillCcySet.contains("USD"), "内部结算单币种不一致时，银行流水币种不为USD");
        }
        // 只允许本币结算或者 USD 结算
        if (ourSettleCctSet.size() == 1) {
            String instBillCcy = instBillCcySet.stream().findFirst()
                .orElseThrow(() -> new BizException(ReconErrorCode.ACTUAL_FUNDS_ALLOCATE_CONTEXT_VALIDATE_ERROR.getCode(), "银行流水币种不一致"));
            String ourSettleCcy = ourSettleCctSet.stream().findFirst()
                .orElseThrow(() -> new BizException(ReconErrorCode.ACTUAL_FUNDS_ALLOCATE_CONTEXT_VALIDATE_ERROR.getCode(), "内部结算单币种不一致"));
            AssertUtil.isTrue(instBillCcy.equals(ourSettleCcy) || "USD".equals(instBillCcy), "只允许本币结算或者 USD 结算");
        }
        AssertUtil.isTrue(context.getTotalExpectSettleAmount().compareTo(BigDecimal.ZERO) > 0, "待分配金额不能小于0");
        AssertUtil.isTrue(context.getTotalInstPaymentAmount().compareTo(BigDecimal.ZERO) > 0, "银行流水支付金额不能小于0");

    }


    /**
     * 构建原始数据
     */
    private void buildContextOriginData(ActualFundsAllocatorContext context){
        log.info("开始构建原始数据...");
        List<FundsReconcileRelationItem> relationItems = context.getRelationItemList();
        // 对账关联分组
        Map<RelationItemTypeEnum, List<FundsReconcileRelationItem>> relationItemGroup = relationItems.stream().collect(Collectors.groupingBy(FundsReconcileRelationItem::getItemType));

        // 银行流水分组
        Map<BankPayDirectionEnum, List<FundsReconcileRelationItem>> instBillDirectionGroup = relationItemGroup.getOrDefault(RelationItemTypeEnum.INST_BILL
                , Collections.emptyList()).stream().collect(Collectors.groupingBy(FundsReconcileRelationItem::getPayDirection));


        List<FundsReconcileRelationItem> chargeOnLongList = extractItemsBySubType(relationItemGroup, RelationItemTypeEnum.CHARGE_ON, SettleTradeOrderRelationFeeEnum.ADD_LONG_AMOUNT);
        // 长款销账关联原银行流水
        Map<String, FundsReconcileRelationItem> chargeOffLongOriginItemMap = queryChargeOffLongOriginInstBill(context.getChargeOffLongList());
        // 长款挂账分组，未关联银行流水的单独一组，用于后续轧差时可以准确的找到对应银行流水
        Map<String, List<FundsReconcileRelationItem>> chargeOnLongItemGroup = chargeOnLongList.stream()
                .collect(Collectors.groupingBy( item -> StringUtils.isEmpty(item.getQuotationBizNo()) ? ActualFundsAllocateCommonUtils.NULL_BIZ_NO_GROUP_KEY : item.getQuotationBizNo()));

        // 本次关联的内部结算单
        List<String> ourSettleOrderNoList = getBizNoList(relationItemGroup, RelationItemTypeEnum.OUR_SETTLE_ORDER);
        List<ClearingSettleOrder> ourSettleOrderList = queryOurSettleOrderList(ourSettleOrderNoList);

        // 获取短款销账关联的内部结算单
        List<FundsReconcileRelationItem> chargeOffShortList = extractItemsBySubType(relationItemGroup, RelationItemTypeEnum.CHARGE_OFF, SettleTradeOrderRelationFeeEnum.OFFSET_SHORT_AMOUNT);
        List<String> chargeOffShortBizNoList = chargeOffShortList.stream().map(FundsReconcileRelationItem::getQuotationBizNo).filter(Objects::nonNull).collect(Collectors.toList());
        List<FundsReconcileRelationItem> chargeOffShortOriginItemList = reconcileRelationItemRepository.findByBizNoList(chargeOffShortBizNoList);
        List<ClearingSettleOrder> chargeOffShortOriginSettleOrderList = findChargeOffShortOriginSettleOrderList(chargeOffShortOriginItemList);

        // 提取各类关联项
        context.setRelationItemList(relationItems)
                // 银行流水
                .setInstBillList(relationItemGroup.getOrDefault(RelationItemTypeEnum.INST_BILL, Collections.emptyList()))
                .setInstBillDirectionGroup(instBillDirectionGroup)
                // 内部结算单
                .setOurSettleOrderList(ourSettleOrderList)
                .setOurSettleOrderItemList(relationItemGroup.getOrDefault(RelationItemTypeEnum.OUR_SETTLE_ORDER, Collections.emptyList()))
                // 挂销账
                .setChargeOnLongList(chargeOnLongList)
                .setChargeOnShortList(extractItemsBySubType(relationItemGroup, RelationItemTypeEnum.CHARGE_ON, SettleTradeOrderRelationFeeEnum.ADD_SHORT_AMOUNT))
                .setChargeOffLongList(extractItemsBySubType(relationItemGroup, RelationItemTypeEnum.CHARGE_OFF, SettleTradeOrderRelationFeeEnum.OFFSET_LONG_AMOUNT))
                .setChargeOffShortList(chargeOffShortList)
                // 挂销账时关联的源数据
                .setChargeOnLongItemGroup(chargeOnLongItemGroup)
                .setChargeOffLongOriginItemMap(chargeOffLongOriginItemMap)
                .setChargeOffShortOriginItemList(chargeOffShortOriginItemList)
                .setChargeOffShortOriginSettleOrderList(chargeOffShortOriginSettleOrderList);
    }



    /**
     * 构建金额数据
     */
    private void buildContextAmountData(ActualFundsAllocatorContext context) {
        log.info("开始构建金额数据");
        Map<BankPayDirectionEnum, List<FundsReconcileRelationItem>> instBillDirectionGroup = context.getInstBillDirectionGroup();

        // 银行流水结算币种
        String settleCurrency = getSingleCcyFromList(context.getInstBillList(), FundsReconcileRelationItem::getCurrency).orElseThrow(() -> new IllegalStateException("银行流水币种不唯一或为空"));

        // ------ 打款金额 ------
        // 银行支付费用
        BigDecimal totalInstPaymentAmount = instBillDirectionGroup.getOrDefault(BankPayDirectionEnum.CREDIT, Collections.emptyList()).stream().map(FundsReconcileRelationItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 长款挂账金额
        BigDecimal totalChargeOnLongAmount = CalculateUtils.sumListAmount(context.getChargeOnLongList(), FundsReconcileRelationItem::getAmount);
        // 长款销账金额
        BigDecimal totalChargeOffLongAmount = CalculateUtils.sumListAmount(context.getChargeOffLongList(), FundsReconcileRelationItem::getAmount);

        // ------ 结算金额 ------
        // 本次结算单金额(换汇后)
        BigDecimal totalSettleOrderAmount = CalculateUtils.sumListAmount(context.getOurSettleOrderItemList(), item -> getOurSettleOrderAmount(item, settleCurrency));
        // 短款销账金额
        BigDecimal totalChargeOffShortAmount = CalculateUtils.sumListAmount(context.getChargeOffShortList(), item -> getOurSettleOrderAmount(item, settleCurrency));
        // 短款挂账金额
        BigDecimal totalNormalChargeOnShortAmount = CalculateUtils.sumListAmount(context.getChargeOnShortList(), item -> getOurSettleOrderAmount(item, settleCurrency));
        // 当长款挂账未关联银行流水时，当做短款挂账处理
        BigDecimal totalSpecialChargeOnShortAmount = CalculateUtils.sumListAmount(context.getChargeOffShortList(), item -> getOurSettleOrderAmount(item, settleCurrency));
        BigDecimal totalChargeOnShortAmount = totalNormalChargeOnShortAmount.subtract(totalChargeOffShortAmount).add(totalSpecialChargeOnShortAmount);


        // ------ 其他费用 ------
        // 银行收取金额
        BigDecimal totalInstChargeAmount = CalculateUtils.sumListAmount(instBillDirectionGroup.getOrDefault(BankPayDirectionEnum.DEBIT, Collections.emptyList()), FundsReconcileRelationItem::getAmount);


        // 计算双边总金额
        // 实结金额 = 银行支付金额 - 长款挂账金额 + 长款销账金额
        BigDecimal totalSettleAmount = totalInstPaymentAmount.subtract(totalChargeOnLongAmount).add(totalChargeOffLongAmount);
        log.info("实结金额[{}] = 银行支付金额[{}] - 长款挂账金额[{}] + 长款销账金额[{}]", totalSettleAmount, totalInstPaymentAmount, totalChargeOnLongAmount, totalChargeOffLongAmount);
        // 应收金额 = 内部结算单金额 - 短款挂账金额 + 短款销账金额
        BigDecimal totalExpectSettleAmount = totalSettleOrderAmount.subtract(totalChargeOnShortAmount).add(totalChargeOffShortAmount);
        log.info("应收金额[{}] = 内部结算单金额(已包括短款销账)[{}] - 短款挂账金额[{}] + 短款销账金额[{}]", totalExpectSettleAmount, totalSettleOrderAmount, totalChargeOnShortAmount, totalChargeOffShortAmount);


        // 汇总金额
        context.setSettleCurrency(settleCurrency)
                // 总金额
                .setTotalExpectSettleAmount(totalExpectSettleAmount)
                .setTotalSettleAmount(totalSettleAmount)
                .setCurrentRelationSettleAmount(totalSettleOrderAmount)
                // 渠道应付 | 应收
                .setTotalInstPaymentAmount(totalInstPaymentAmount)
                .setTotalInstChargeAmount(totalInstChargeAmount)
                // 挂销账金额
                .setTotalChargeOnLongAmount(totalChargeOnLongAmount)
                .setTotalChargeOffLongAmount(totalChargeOffLongAmount)
                .setTotalChargeOnShortAmount(totalChargeOnShortAmount)
                .setTotalChargeOffShortAmount(totalChargeOffShortAmount);
    }


    /**
     * 获取短款销账关联的内部结算单
     */
    private List<ClearingSettleOrder> findChargeOffShortOriginSettleOrderList(List<FundsReconcileRelationItem> chargeOffShortOriginItemList) {
        if(CollectionUtils.isEmpty(chargeOffShortOriginItemList)){
            return Collections.emptyList();
        }
        List<String> relationList = chargeOffShortOriginItemList.stream().map(FundsReconcileRelationItem::getRelationNo).distinct().collect(Collectors.toList());
        List<FundsReconcileRelationItem> originRelationList = reconcileRelationItemRepository.findByRelationNoList(relationList);
        List<String> chargeOffShortSettleOrderNoList = originRelationList.stream()
                .filter(item -> RelationItemTypeEnum.OUR_SETTLE_ORDER.equals(item.getItemType()))
                .map(FundsReconcileRelationItem::getBizNo)
                .distinct()
                .collect(Collectors.toList());
        return queryOurSettleOrderList(chargeOffShortSettleOrderNoList);
    }


    /**
     * 获取长款销账关联的银行流水
     */
    private Map<String, FundsReconcileRelationItem> queryChargeOffLongOriginInstBill(List<FundsReconcileRelationItem> chargeOffLongList) {
        if(CollectionUtils.isEmpty(chargeOffLongList)){
            return Collections.emptyMap();
        }
        List<String> quotationNoList = chargeOffLongList.stream().map(FundsReconcileRelationItem::getQuotationBizNo).collect(Collectors.toList());
        // 查询销账长款对应银行流水
        Map<String, FundsReconcileRelationItem> chargeOffLongInstBillMap = reconcileRelationItemRepository
                .findByBizNoList(quotationNoList).stream().collect(Collectors.toMap(
                        FundsReconcileRelationItem::getBizNo, Function.identity(),
                        (v1, v2) -> ExceptionUtils.throwOnDuplicate(v1, v2, ReconErrorCode.ACTUAL_FUNDS_ALLOCATE_DUPLICATE_ITEM, "发现重复银行流水: " + v1.getBizNo())
                ));
        // 关联销账单号及银行流水
        return chargeOffLongList.stream()
                .collect(Collectors.toMap(
                        FundsReconcileRelationItem::getBizNo,
                        item -> Optional.ofNullable(chargeOffLongInstBillMap.get(item.getQuotationBizNo()))
                                .orElseThrow(() -> new BizException("MISSING_BANK_RECORD","未找到对应的银行流水: " + item.getQuotationBizNo())),
                        (v1, v2) -> ExceptionUtils.throwOnDuplicate(v1, v2, ReconErrorCode.ACTUAL_FUNDS_ALLOCATE_DUPLICATE_ITEM, "发现重复银行流水: " + v1.getBizNo())
                ));
    }


    private Map<String, List<ActualFundsAllocateDetail>> queryClearingReconResultDetail(List<ClearingReconcileResult> clearingReconcileResultList) {
        List<String> resultIdList = clearingReconcileResultList.stream().map(ClearingReconcileResult::getResultId).collect(Collectors.toList());
        List<List<String>> partition = Lists.partition(resultIdList, 300);
        return partition.stream().map(allocateDetailRepository::findByClearingReconResultIdList)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (v1, v2) -> ExceptionUtils.throwOnDuplicate(v1, v2, ReconErrorCode.ACTUAL_FUNDS_ALLOCATE_DUPLICATE_ITEM, "重复查询外部流水拆分明细")
                ));
    }

    private List<ClearingSettleOrder> queryOurSettleOrderList(List<String> parentNoList){
        log.info("查询内部结算单, 父单号: {}", parentNoList);
        if (CollectionUtils.isEmpty(parentNoList)){
            return Collections.emptyList();
        }
        ClearingSettleOrderCondition condition = new ClearingSettleOrderCondition();
        condition.setParentNoList(parentNoList);
        return clearingSettleOrderRepository.findByCondition(condition);
    }


    /**
     * 获取列表币种
     */
    private <T> Optional<String> getSingleCcyFromList(List<T> list, Function<T, String> keyExtractor){
        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        }
        Set<String> ccySet = list.stream().map(keyExtractor).collect(Collectors.toSet());
        AssertUtil.isTrue(ccySet.size() == 1, "关键业务列表中存在多个币种或币种为空: " + ccySet);
        return ccySet.stream().findFirst();
    }

    private List<String> getBizNoList(Map<RelationItemTypeEnum, List<FundsReconcileRelationItem>> relationItemGroup, RelationItemTypeEnum itemType) {
        return relationItemGroup.getOrDefault(itemType, Collections.emptyList()).stream()
                .map(FundsReconcileRelationItem::getBizNo)
                .distinct()
                .collect(Collectors.toList());
    }


    private BigDecimal getOurSettleOrderAmount(FundsReconcileRelationItem item, String settleCurrency){
        String currency = item.getCurrency();
        BigDecimal amount = item.getAmount();
        BigDecimal exchangeRate = Optional.ofNullable(item.getExchangeRate()).orElse(new BigDecimal("1"));
        if (currency.equalsIgnoreCase(settleCurrency)){
            return amount;
        }else{
            return amount.multiply(exchangeRate);
        }
    }

    /**
     * 从分组中提取特定子类型的关联项列表
     */
    private List<FundsReconcileRelationItem> extractItemsBySubType(Map<RelationItemTypeEnum, List<FundsReconcileRelationItem>> relationItemGroup, RelationItemTypeEnum itemType, SettleTradeOrderRelationFeeEnum subTypeEnum) {
        return relationItemGroup.getOrDefault(itemType, Collections.emptyList()).stream()
                .filter(item -> item.getSubType().equals(subTypeEnum.getSubType()))
                .collect(Collectors.toList());
    }
}
