package com.payermax.funds.reconcile.core.app.service.allocate;

import com.payermax.funds.reconcile.core.app.converter.ActualFundsAllocatorConvert;
import com.payermax.funds.reconcile.core.app.model.actualFundsSplit.ActualFundsAllocatorContext;
import com.payermax.funds.reconcile.core.app.model.actualFundsSplit.FundsAllocatorResult;
import com.payermax.funds.reconcile.core.app.service.allocate.handler.ActualFundsAllocateCommonUtils;
import com.payermax.funds.reconcile.core.app.service.allocate.handler.ActualFundsAllocateTaskHandler;
import com.payermax.funds.reconcile.core.app.service.allocate.handler.ActualFundsAllocator;
import com.payermax.funds.reconcile.core.app.service.allocate.handler.ActualFundsAllocatorContextBuilder;
import com.payermax.funds.reconcile.core.common.constants.ReconCommonConstants;
import com.payermax.funds.reconcile.core.common.utils.GenerateUtil;
import com.payermax.funds.reconcile.core.domain.domain.ActualFundsAllocateDetail;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingReconcileResult;
import com.payermax.funds.reconcile.core.domain.domain.funds.FundsReconcileRelation;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.ClearingReconcileResultRepository;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.FundsAllocateDetailRepository;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.FundsReconcileRelationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/13
 * @description 实体资金拆分
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActualFundsAllocateService {

    private final FundsReconcileRelationRepository reconcileRelationRepository;
    private final ActualFundsAllocatorContextBuilder contextBuilder;
    private final ActualFundsAllocateTaskHandler taskHandler;
    private final ActualFundsAllocator allocator;
    private final ActualFundsAllocateCommonUtils commonUtils;
    private final FundsAllocateDetailRepository allocateDetailRepository;
    private final ClearingReconcileResultRepository clearingReconcileResultRepository;
    private final TransactionTemplate transactionTemplate;




    /**
     * 生成待办任务
     */
    public void generateAllocatorTask(Date start, Date end){
        taskHandler.generateTask(start, end);
    }


    /**
     * 处理待办任务
     */
    public void processAllocatorTasks() {
        taskHandler.processPendingTasks(this::singleFundsRelationAllocator);
    }


    public void singleFundsRelationAllocator(String relationBizId){
        FundsReconcileRelation relation = reconcileRelationRepository.findByRelationNo(relationBizId);
        // 上下文构建
        ActualFundsAllocatorContext context = contextBuilder.build(relation);

        // 开始拆分计算
        allocator.allocate(context);

        // 保存结果
        saveAllocationResults(context);
    }

    private void saveAllocationResults(ActualFundsAllocatorContext context) {
        List<FundsAllocatorResult> resultList = context.getFundsAllocatorResultList();

        // 分区保存
        List<List<FundsAllocatorResult>> resultPartitions = commonUtils.commonPartition(resultList);
        log.info("RelationNo: {}, 准备保存...", context.getRelation().getRelationNo());

        for (List<FundsAllocatorResult> resultPartition : resultPartitions) {
            List<FundsAllocatorResult> writeableList = resultPartition.stream().filter(FundsAllocatorResult::getNewly).collect(Collectors.toList());
            // 业务单号补全
            List<ActualFundsAllocateDetail> detailList = writeableList.stream().map(FundsAllocatorResult::getAllocatorDetails)
                    .flatMap(Collection::stream)
                    .peek(item -> item.setBizNo(GenerateUtil.getId(ReconCommonConstants.NoPrefix.ACTUAL_FUNDS_ALLOCATE_DETAIL)))
                    .collect(Collectors.toList());
            List<ClearingReconcileResult> clearingReconcileResult = ActualFundsAllocatorConvert.INSTANCE.fundsAllocatorList2ClearingReconcileResult(writeableList);
            try{
                transactionTemplate.execute(status -> {
                    allocateDetailRepository.saveBatch(detailList);
                    clearingReconcileResultRepository.writeArrivedSettleMsg(clearingReconcileResult);
                    log.info("RelationNo: {}, 保存完成, 本次保存 {} 条明细数据", context.getRelation().getRelationNo(), detailList.size());
                    return Boolean.TRUE;
                });
            }catch (DuplicateKeyException exception){
                log.error("RelationNo: {}, {}", context.getRelation().getRelationNo(), exception.getCause());
            }
        }

    }



}
