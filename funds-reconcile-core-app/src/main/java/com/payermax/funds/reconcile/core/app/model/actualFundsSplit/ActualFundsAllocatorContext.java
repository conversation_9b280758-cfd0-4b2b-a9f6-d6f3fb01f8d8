package com.payermax.funds.reconcile.core.app.model.actualFundsSplit;

import com.payermax.funds.reconcile.core.common.enums.BankPayDirectionEnum;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingSettleOrder;
import com.payermax.funds.reconcile.core.domain.domain.funds.FundsReconcileRelation;
import com.payermax.funds.reconcile.core.domain.domain.funds.FundsReconcileRelationItem;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/16
 * @DESC
 */
@Data
@Accessors(chain = true)
public class ActualFundsAllocatorContext {

    private FundsReconcileRelation relation;
    private List<FundsReconcileRelationItem> relationItemList;

    // 银行流水
    private List<FundsReconcileRelationItem> instBillList;

    // 根据方向分组的银行流水
    private Map<BankPayDirectionEnum, List<FundsReconcileRelationItem>> instBillDirectionGroup;

    // 内部结算单
    private List<FundsReconcileRelationItem> ourSettleOrderItemList;
    private List<ClearingSettleOrder> ourSettleOrderList;

    // 挂账相关
    private List<FundsReconcileRelationItem> chargeOnLongList;
    private List<FundsReconcileRelationItem> chargeOnShortList;
    private List<FundsReconcileRelationItem> chargeOffLongList;
    private List<FundsReconcileRelationItem> chargeOffShortList;

    // 其他费用: 渠道支付手续费 | 其他费用
    private List<FundsReconcileRelationItem> channelPaymentFeeList;
    private List<FundsReconcileRelationItem> otherFeeList;

    private Map<String, List<FundsReconcileRelationItem>> chargeOnLongItemGroup;

    // 长款销账关联的原银行流水, Key: 销账长款 biz_no |  Value: 原银行流水
    private Map<String, FundsReconcileRelationItem> chargeOffLongOriginItemMap;

    // 短款销账关联的原结算单列表 / 原item
    private List<ClearingSettleOrder> chargeOffShortOriginSettleOrderList;
    private List<FundsReconcileRelationItem> chargeOffShortOriginItemList;

    // 流水权重缓存, key: relation_no | value: 流水权重列表
    Map<String, List<FundsAllocatorResult>> clearReconWeightMap = new HashMap<>();

    // 拆分结果
    private List<FundsAllocatorResult> fundsAllocatorResultList;

    private String settleCurrency;

    private BigDecimal totalSettleAmount;

    private BigDecimal currentRelationSettleAmount;

    private BigDecimal totalExpectSettleAmount;

    // 渠道应付 | 应收
    private BigDecimal totalInstPaymentAmount;
    private BigDecimal totalInstChargeAmount;

    // 长短款挂销账
    private BigDecimal totalChargeOnLongAmount;
    private BigDecimal totalChargeOnShortAmount;
    private BigDecimal totalChargeOffLongAmount;
    private BigDecimal totalChargeOffShortAmount;

    // 其他费用
    private BigDecimal totalOtherFeeAmount;
    private BigDecimal totalChannelPaymentFeeAmount;


}
