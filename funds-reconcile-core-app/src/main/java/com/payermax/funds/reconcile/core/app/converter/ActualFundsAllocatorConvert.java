package com.payermax.funds.reconcile.core.app.converter;

import com.payermax.funds.reconcile.core.app.model.actualFundsSplit.FundsAllocatorResult;
import com.payermax.funds.reconcile.core.domain.domain.ActualFundsAllocateDetail;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingReconcileResult;
import com.payermax.funds.reconcile.core.domain.domain.funds.FundsReconcileRelationItem;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/20
 * @DESC
 */
@Mapper(componentModel = "spring", imports = {})
public interface ActualFundsAllocatorConvert {


    ActualFundsAllocatorConvert INSTANCE = Mappers.getMapper(ActualFundsAllocatorConvert.class);

    @Mappings({
            @Mapping(target = "unarrivedAmountWeight", source = "weight"),
            @Mapping(target = "arrivedSettleCurrency", source = "settleCurrency"),
    })
    FundsAllocatorResult buildAllocatorResultWithWeight(ClearingReconcileResult result, String settleCurrency, BigDecimal weight);

    FundsAllocatorResult reconResult2AllocatorResult(ClearingReconcileResult result);

    @Mappings({
            @Mapping(target = "bizNo", ignore = true),
            @Mapping(target = "clearingReconcileResultId", source = "allocatorResult.resultId"),
            @Mapping(target = "clearingSettleOrderNo", source = "allocatorResult.clearingSettleOrder.settleOrderNo"),
            @Mapping(target = "fundsReconcileResultId", source = "sourceRelationItem.bizNo"),
            @Mapping(target = "sourceRelationNo", source = "sourceRelationItem.relationNo"),
            @Mapping(target = "sourceBizNo", source = "sourceRelationItem.bizNo"),
            @Mapping(target = "weight", source = "weight"),
            @Mapping(target = "amount", source = "allocatorAmount"),
            @Mapping(target = "status", expression = "java(1)"),
            @Mapping(target = "sourceType", source = "relationItem.itemType"),
            @Mapping(target = "sourceSubType", source = "relationItem.subType"),
            @Mapping(target = "currency", source = "relationItem.currency"),
            @Mapping(target = "fundsAccountId", source = "sourceRelationItem.fundsAccountId"),
            @Mapping(target = "settleTime", source = "sourceRelationItem.runningTime"),
            @Mapping(target = "description", source = "description"),
    })
    ActualFundsAllocateDetail buildAllocatorDetail(FundsAllocatorResult allocatorResult, FundsReconcileRelationItem relationItem, FundsReconcileRelationItem sourceRelationItem
            , BigDecimal weight, BigDecimal allocatorAmount, String description);

    @Named("deepCopy")
    FundsAllocatorResult deepCopy(FundsAllocatorResult allocatorResult);

    @IterableMapping(qualifiedByName = "deepCopy")
    List<FundsAllocatorResult> deepCopyList(List<FundsAllocatorResult> result);

    List<ClearingReconcileResult> fundsAllocatorList2ClearingReconcileResult(List<FundsAllocatorResult> result);
}
