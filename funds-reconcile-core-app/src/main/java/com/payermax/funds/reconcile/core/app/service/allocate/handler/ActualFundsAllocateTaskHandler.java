package com.payermax.funds.reconcile.core.app.service.allocate.handler;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.funds.reconcile.core.common.constants.ReconCommonConstants;
import com.payermax.funds.reconcile.core.common.constants.RedisKey;
import com.payermax.funds.reconcile.core.common.enums.CommonTaskStatusEnum;
import com.payermax.funds.reconcile.core.common.enums.CommonTaskTypeEnum;
import com.payermax.funds.reconcile.core.common.enums.RelationOperationTypeEnum;
import com.payermax.funds.reconcile.core.common.utils.GenerateUtil;
import com.payermax.funds.reconcile.core.common.utils.RedisLockUtils;
import com.payermax.funds.reconcile.core.domain.domain.CommonTask;
import com.payermax.funds.reconcile.core.domain.domain.funds.FundsReconcileRelation;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.CommonTaskRepository;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.FundsReconcileRelationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2025/6/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ActualFundsAllocateTaskHandler {

    private final FundsReconcileRelationRepository reconcileRelationRepository;
    private final CommonTaskRepository commonTaskRepository;
    private final RedisLockUtils redisLockUtils;

    @NacosValue(value = "${actual.funds.allocator.task.timeout:7200}", autoRefreshed = true)
    private String taskTimeout;
    @NacosValue(value = "${actual.funds.allocator.task.scanInterval:2}", autoRefreshed = true)
    private Integer scanInterval;


    /**
     * 生成拆分任务
     */
    public void generateTask(Date start, Date end){
        log.info("开始生成实体资金拆分任务...");
        // 获取时间区间内对账关联
        List<FundsReconcileRelation> relationList = queryReconRelation(start, end);
        if  (relationList.isEmpty()) {
            log.info("无待处理对账关联");
            return;
        }
        // 生成任务
        relationList.forEach(item -> {
            saveTask(item.getRelationNo());
            log.info("生成任务成功: {}", item.getRelationNo());
        });

    }

    public void processPendingTasks(Consumer<String> coreProcessor){
        // 捞取任务
        log.info("开始捞取实体资金拆分任务...");
        List<CommonTask> pendingTasks = commonTaskRepository.findPendingTasksByType(CommonTaskTypeEnum.ACTUAL_FUNDS_ALLOCATE);
        if(CollectionUtils.isEmpty(pendingTasks)){
            log.info("无待处理任务");
            return;
        }

        // 处理任务
        log.info("发现 {} 个待处理任务，开始处理...", pendingTasks.size());
        long failCount = pendingTasks.stream().map(task -> processSingleTask(task, coreProcessor)).filter(item -> !item).count();
        if(failCount > 0){
            log.error("处理任务存在失败，失败数量: {}", failCount);
        }
        log.info("本轮实体资金拆分任务处理完毕！");
    }

    /**
     * 处理拆分任务
     */
    public boolean processSingleTask(CommonTask task, Consumer<String> coreProcessor){
        String taskMsg = String.format("任务号: %s 关联单号: %s ", task.getTaskId(), task.getRelationBizId());
        log.info("开始处理任务, {}...", taskMsg);
        // 获取分布式锁
        boolean lock = redisLockUtils.lock(RedisKey.getCommonTaskLockKey(task.getRelationBizId()), task.getRelationBizId(), taskTimeout);
        if (!lock) {
            log.warn("获取锁失败, {}...", taskMsg);
            return Boolean.FALSE;
        }
        try {
            // 获取分布式锁成功，开始处理任务
            log.info("获取锁成功，开始处理对账关联: {}...", taskMsg);
            long startTime = System.currentTimeMillis();
            task.setProcessingStartAt(new Date());
            commonTaskRepository.updateByTaskId(task);
            // 核心处理流程
            coreProcessor.accept(task.getRelationBizId());
            // 更新任务状态
            task.setTaskStatus(CommonTaskStatusEnum.SUCCESS);
            commonTaskRepository.updateByTaskId(task);
            long costTime = System.currentTimeMillis() - startTime;
            log.info("处理任务成功, {}, 执行耗时: {} S...", taskMsg, costTime/1000);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("处理任务异常, {}, 错误信息:{} {}", taskMsg, e.getStackTrace(), e.getMessage());
            return Boolean.FALSE;
        } finally {
            redisLockUtils.unLock(RedisKey.getCommonTaskLockKey(task.getRelationBizId()), task.getRelationBizId());
        }

    }


    /**
     * 保存任务
     */
    private void saveTask(String relationNo) {
        try {
            log.info("开始保存任务");
            CommonTask commonTask = new CommonTask();
            commonTask.setTaskId(GenerateUtil.getId(ReconCommonConstants.NoPrefix.COMMON_TASK))
                    .setRelationBizId(relationNo)
                    .setTaskType(CommonTaskTypeEnum.ACTUAL_FUNDS_ALLOCATE)
                    .setTaskStatus(CommonTaskStatusEnum.INIT)
                    .setTaskData("{}")
                    .setRetryCount(0)
                    .setProcessingStartAt(new Date())
                    .setErrorMessage("");

            commonTaskRepository.saveWithCheck(commonTask);
        } catch (Exception e) {
            log.error("保存任务失败: {}", e.getMessage());
        }

    }

    /**
     * 查询时间区间内的对账关联
     */
    private List<FundsReconcileRelation> queryReconRelation(Date start, Date end){
        // 仅查询代收
        List<RelationOperationTypeEnum> operationTypeList = Collections.singletonList(RelationOperationTypeEnum.OUR_SETTLE_ORDER_RECON);
        return reconcileRelationRepository.queryByOperationTypeAndTimeRange(operationTypeList, start, end);
    }
}
