package com.payermax.funds.reconcile.core.app.service.allocate.handler;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Lists;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.funds.reconcile.core.app.converter.ActualFundsAllocatorConvert;
import com.payermax.funds.reconcile.core.app.model.actualFundsSplit.FundsAllocatorResult;
import com.payermax.funds.reconcile.core.common.enums.ReconErrorCode;
import com.payermax.funds.reconcile.core.common.enums.RelationItemTypeEnum;
import com.payermax.funds.reconcile.core.common.utils.ExceptionUtils;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingReconcileResult;
import com.payermax.funds.reconcile.core.domain.domain.clearing.ClearingSettleOrder;
import com.payermax.funds.reconcile.core.domain.domain.funds.FundsReconcileRelationItem;
import com.payermax.funds.reconcile.core.infrastructure.repository.condition.ClearingSettleOrderCondition;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.ClearingReconcileResultRepository;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.ClearingSettleOrderRepository;
import com.payermax.funds.reconcile.core.infrastructure.repository.repository.FundsReconcileRelationItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2025/6/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ActualFundsAllocateCommonUtils {

    private final ClearingReconcileResultRepository clearingReconcileResultRepository;
    private final FundsReconcileRelationItemRepository reconcileRelationItemRepository;
    private final ClearingSettleOrderRepository clearingSettleOrderRepository;
    private final ActualFundsAllocatorConvert convert;

    @NacosValue(value = "${actual.funds.allocator.task.common.batchSize:100}", autoRefreshed = true)
    private Integer batchSize;
    @NacosValue(value = "${actual.funds.allocator.task.common.pageSize:100}", autoRefreshed = true)
    private Integer pageSize;

    public static final String NULL_BIZ_NO_GROUP_KEY = "NULL_BIZ_NO_GROUP";
    public static final int DEFAULT_SCALE = 8;
    public static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;


    public <T> List<List<T>> commonPartition(List<T> list){
        return Lists.partition(list, batchSize);
    }

    public BigDecimal divide(BigDecimal dividend, BigDecimal divisor){
        if (divisor.compareTo(BigDecimal.ZERO) == 0){
            return BigDecimal.ZERO;
        }
        return dividend.divide(divisor, DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    public BigDecimal multiply(BigDecimal multiplicand, BigDecimal multiplier){
        return multiplicand.multiply(multiplier).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    public List<ClearingSettleOrder> getSettleOrderByRelationList(List<FundsReconcileRelationItem> relationList){
        List<String> chargeOffShortSettleOrderNoList = getItemByType(relationList, RelationItemTypeEnum.OUR_SETTLE_ORDER).stream()
                .map(FundsReconcileRelationItem::getBizNo).distinct().collect(Collectors.toList());
        return queryOurSettleOrderList(chargeOffShortSettleOrderNoList);
    }

    public List<FundsReconcileRelationItem> getItemByType(List<FundsReconcileRelationItem> itemList, RelationItemTypeEnum type){
        return itemList.stream()
                .filter(item -> type.equals(item.getItemType()))
                .collect(Collectors.toList());
    }

    private List<ClearingSettleOrder> queryOurSettleOrderList(List<String> parentNoList){
        log.info("查询内部结算单, 父单号: {}", parentNoList);
        if (CollectionUtils.isEmpty(parentNoList)){
            return Collections.emptyList();
        }
        ClearingSettleOrderCondition condition = new ClearingSettleOrderCondition();
        condition.setParentNoList(parentNoList);
        return clearingSettleOrderRepository.findByCondition(condition);
    }

    /**
     * 根据结算单构造拆分结果
     */
    public List<FundsAllocatorResult> getAllocateResultBySettleOrder(List<ClearingSettleOrder> orderList){
        List<String> settleOrderNoList = orderList.stream().map(ClearingSettleOrder::getSettleOrderNo).collect(Collectors.toList());
        Map<String, ClearingSettleOrder> settleOrderMap = orderList.stream().collect(Collectors.toMap(ClearingSettleOrder::getSettleOrderNo, Function.identity(),
                (v1, v2) -> ExceptionUtils.throwOnDuplicate(v1, v2, ReconErrorCode.ACTUAL_FUNDS_ALLOCATE_CONTEXT_VALIDATE_ERROR, String.format("存在重复结算单:%s", v1.getSettleOrderNo()))));
        List<ClearingReconcileResult> clearingReconcileResults = fetchAllClearingReconcileResult(settleOrderNoList);
        return clearingReconcileResults.stream().map(convert::reconResult2AllocatorResult).peek( item -> {
            ClearingSettleOrder settleOrder = Optional.ofNullable(settleOrderMap.get(item.getSettleOrderNo())).orElseThrow(() -> new BusinessException(ReconErrorCode.ACTUAL_FUNDS_ALLOCATE_VALIDATE_ERROR.getCode(), "外部流水结算单不存在"));
            item.setClearingSettleOrder(settleOrder);
        }).collect(Collectors.toList());
    }

    /**
     * 用于获取结算单对应的 Item，key: biz_no
     */
    public Map<String, FundsReconcileRelationItem> getRelationItemSettleOrderMap(List<FundsReconcileRelationItem> itemList){
        return itemList.stream().collect(Collectors.toMap(FundsReconcileRelationItem::getBizNo
                , Function.identity(), (v1, v2) ->
                        ExceptionUtils.throwOnDuplicate(v1, v2, ReconErrorCode.ACTUAL_FUNDS_ALLOCATE_CONTEXT_VALIDATE_ERROR, String.format("存在重复内部结算单:%s", v1.getBizNo() ))));
    }

    public List<ClearingReconcileResult> fetchAllClearingReconcileResult(List<String> settleOrderNoList){
        int total = clearingReconcileResultRepository.queryCountBySettleOrderNoList(settleOrderNoList);
        if (CollectionUtils.isEmpty(settleOrderNoList) || total <= 0) {
            return Collections.emptyList();
        }
        int totalPages = (total + pageSize - 1) / pageSize;
        log.info("查询结算单关联外部流水, 结算单号: {}, 总数: {}, 总页数: {}", settleOrderNoList, total, totalPages);

        return IntStream.rangeClosed(1, totalPages)
                .mapToObj(pageNum -> clearingReconcileResultRepository.querySettleMsgBySettleOrderNoList(settleOrderNoList, pageNum, pageSize))
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

}
